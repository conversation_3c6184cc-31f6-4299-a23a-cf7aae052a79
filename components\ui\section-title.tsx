"use client";

import { motion, useInView } from "framer-motion";
import { useRef } from "react";

interface SectionTitleProps {
  subtitle?: string;
  title: string;
  highlightWord?: string;
  description?: string;
  icon?: React.ReactNode;
  alignment?: "left" | "center" | "right";
  size?: "small" | "medium" | "large" | "hero";
  className?: string;
}

export const SectionTitle = ({
  subtitle,
  title,
  highlightWord,
  description,
  icon,
  alignment = "center",
  size = "medium",
  className = ""
}: SectionTitleProps) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  // Size configurations
  const sizeConfig = {
    small: {
      subtitle: "text-sm",
      title: "text-3xl md:text-4xl",
      description: "text-base",
      spacing: "mb-8"
    },
    medium: {
      subtitle: "text-sm md:text-base",
      title: "text-4xl md:text-5xl lg:text-6xl",
      description: "text-lg md:text-xl",
      spacing: "mb-12"
    },
    large: {
      subtitle: "text-base md:text-lg",
      title: "text-5xl md:text-6xl lg:text-7xl",
      description: "text-xl md:text-2xl",
      spacing: "mb-16"
    },
    hero: {
      subtitle: "text-lg md:text-xl",
      title: "text-6xl md:text-7xl lg:text-8xl",
      description: "text-2xl md:text-3xl",
      spacing: "mb-20"
    }
  };

  const config = sizeConfig[size];
  const alignmentClass = alignment === "left" ? "text-left" : alignment === "right" ? "text-right" : "text-center";

  // Split title to highlight specific word
  const titleParts = highlightWord ? title.split(highlightWord) : [title];

  return (
    <motion.div
      ref={ref}
      className={`${config.spacing} ${alignmentClass} ${className}`}
      initial={{ opacity: 0, y: 30 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      {/* Subtitle with glassmorphism badge */}
      {subtitle && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className={`inline-flex items-center gap-3 mb-6 ${alignment === "left" ? "" : "mx-auto"}`}
        >
          <div className="Welcome-box py-3 px-6 border border-[#7042f88b] backdrop-blur-xl">
            <div className="flex items-center gap-3">
              {icon && (
                <motion.div
                  initial={{ rotate: 0 }}
                  animate={isInView ? { rotate: 360 } : { rotate: 0 }}
                  transition={{ duration: 1, delay: 0.5 }}
                  className="text-purple-400"
                >
                  {icon}
                </motion.div>
              )}
              <span className={`Welcome-text ${config.subtitle} font-medium`}>
                {subtitle}
              </span>
            </div>
          </div>
        </motion.div>
      )}

      {/* Main title with gradient and animations */}
      <motion.h2
        className={`${config.title} font-bold text-white leading-tight`}
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        {highlightWord ? (
          <span>
            {titleParts[0]}
            <motion.span
              className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-600 relative"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              {highlightWord}
              {/* Animated underline */}
              <motion.div
                className="absolute -bottom-2 left-0 h-1 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full"
                initial={{ width: 0 }}
                animate={isInView ? { width: "100%" } : { width: 0 }}
                transition={{ duration: 1, delay: 0.8 }}
              />
            </motion.span>
            {titleParts[1]}
          </span>
        ) : (
          <motion.span
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            {title}
          </motion.span>
        )}
      </motion.h2>

      {/* Decorative elements */}
      <motion.div
        className={`flex items-center gap-4 mt-6 ${alignment === "center" ? "justify-center" : alignment === "right" ? "justify-end" : "justify-start"}`}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
        <motion.div
          className="w-12 h-0.5 bg-gradient-to-r from-purple-500 to-transparent rounded-full"
          initial={{ width: 0 }}
          animate={isInView ? { width: 48 } : { width: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        />
        <motion.div
          className="w-2 h-2 rounded-full bg-purple-500"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: 1
          }}
        />
        <motion.div
          className="w-8 h-0.5 bg-gradient-to-r from-cyan-500 to-transparent rounded-full"
          initial={{ width: 0 }}
          animate={isInView ? { width: 32 } : { width: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
        />
      </motion.div>

      {/* Description */}
      {description && (
        <motion.p
          className={`${config.description} text-gray-300 leading-relaxed mt-6 max-w-3xl ${alignment === "center" ? "mx-auto" : ""}`}
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          {description}
        </motion.p>
      )}

      {/* Background glow effect */}
      <motion.div
        className="absolute -inset-x-20 -inset-y-10 bg-gradient-to-r from-purple-500/5 via-cyan-500/5 to-purple-500/5 rounded-full blur-3xl pointer-events-none"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
        transition={{ duration: 1.5, delay: 1 }}
      />
    </motion.div>
  );
};
