"use client";

import {
  CheckCircleIcon,
  LightBulbIcon,
  SparklesIcon,
  StarIcon,
  RocketLaunchIcon
} from "@heroicons/react/24/outline";
import { motion, useInView } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import React, { useRef, useState } from "react";

import { SectionTitle } from "@/components/ui/section-title";
import {
  slideInFromLeft,
  slideInFromRight,
  slideInFromTop,
} from "@/lib/motion";

// Function to get a different icon for each feature
const getFeatureIcon = (index: number) => {
  const icons = [
    CheckCircleIcon,
    StarIcon,
    LightBulbIcon,
    SparklesIcon,
    RocketLaunchIcon
  ];
  return icons[index % icons.length];
};

// Service card component with hover effects and animations
const ServiceCard = ({
  title,
  description,
  iconSrc,
  index,
  features,
}: {
  title: string;
  description: string;
  iconSrc: string;
  index: number;
  features: string[];
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true });

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.5, delay: index * 0.2 }}
      className="relative group w-full sm:w-[calc(100%-1rem)] md:w-[calc(50%-2rem)] lg:w-[calc(50%-2rem)] xl:w-[calc(25%-2rem)] min-h-[400px] sm:min-h-[450px] mx-2 sm:mx-4 my-4 sm:my-6 perspective"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={() => setIsHovered(!isHovered)} // For mobile touch
    >
      <div
        className={`relative w-full h-full transition-all duration-500 preserve-3d ${
          isHovered ? "rotate-y-180" : ""
        }`}
      >
        {/* Front of card */}
        <div className="absolute w-full h-full backface-hidden">
          <div className="flex flex-col items-center justify-center p-4 sm:p-6 md:p-8 bg-gradient-to-b from-[rgba(30,10,60,0.5)] to-[rgba(3,0,20,0.7)] rounded-xl border border-[#7042F88B] h-full shadow-lg shadow-purple-900/20 overflow-hidden backdrop-blur-lg">
            {/* Enhanced animated background elements with improved blur */}
            <div className="absolute top-0 right-0 w-24 sm:w-32 h-24 sm:h-32 bg-purple-500/15 rounded-full blur-3xl -mr-12 sm:-mr-16 -mt-12 sm:-mt-16 group-hover:bg-purple-500/25 transition-all duration-700"></div>
            <div className="absolute bottom-0 left-0 w-24 sm:w-32 h-24 sm:h-32 bg-cyan-500/15 rounded-full blur-3xl -ml-12 sm:-ml-16 -mb-12 sm:-mb-16 group-hover:bg-cyan-500/25 transition-all duration-700"></div>

            {/* Service icon with animation */}
            <motion.div
              className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 relative mb-4 sm:mb-6"
              animate={{
                y: [0, -5, 0, 5, 0],
                scale: [1, 1.05, 1, 1.05, 1],
              }}
              transition={{
                duration: 5,
                repeat: Infinity,
                repeatType: "mirror"
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/30 to-cyan-500/30 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
              <Image
                src={iconSrc}
                alt={title}
                fill
                className="object-contain z-10"
              />
            </motion.div>

            {/* Title with gradient on hover */}
            <h3 className="text-xl sm:text-2xl font-bold text-white mb-2 sm:mb-4 text-center group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-cyan-400 transition-all duration-500">{title}</h3>

            {/* Description */}
            <p className="text-gray-300 text-center text-sm sm:text-base mb-4 sm:mb-6">{description}</p>

            {/* Learn more button with animation */}
            <div className="mt-auto">
              <motion.span
                className="text-xs sm:text-sm text-purple-400 flex items-center cursor-pointer"
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 400 }}
              >
                Learn More
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 sm:h-4 sm:w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </motion.span>
            </div>
          </div>
        </div>

        {/* Back of card */}
        <div className="absolute w-full h-full backface-hidden rotate-y-180">
          <div className="flex flex-col items-center justify-between p-4 sm:p-6 md:p-8 bg-gradient-to-b from-[rgba(30,10,60,0.5)] to-[rgba(3,0,20,0.7)] rounded-xl border border-[#7042F88B] h-full shadow-lg shadow-purple-900/20 backdrop-blur-lg">
            {/* Title with gradient */}
            <h3 className="text-xl sm:text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-cyan-400 mb-2 sm:mb-4 text-center">{title}</h3>

            {/* Features list with animations */}
            <ul className="space-y-2 sm:space-y-3 w-full">
              {features.map((feature, i) => (
                <motion.li
                  key={`feature-${title}-${i}`}
                  className="flex items-start"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: i * 0.1 }}
                >
                  {React.createElement(getFeatureIcon(i), {
                    className: "h-4 w-4 sm:h-5 sm:w-5 text-purple-400 mr-2 flex-shrink-0 mt-0.5"
                  })}
                  <span className="text-gray-300 text-xs sm:text-sm">{feature}</span>
                </motion.li>
              ))}
            </ul>

            {/* CTA button */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="mt-4 sm:mt-6"
            >
              <Link
                href={`/projects?category=${encodeURIComponent(title)}`}
                className="px-4 sm:px-6 py-1.5 sm:py-2 bg-gradient-to-r from-purple-600 to-cyan-600 text-white rounded-full text-xs sm:text-sm hover:opacity-90 transition-opacity inline-block"
              >
                View Projects
              </Link>
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export const Services = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });

  const services = [
    {
      title: "AI Automation",
      description: "Leverage cutting-edge artificial intelligence to automate repetitive tasks, analyze data, and make intelligent decisions for your business.",
      iconSrc: "/skills/artificial-intelligence.png",
      features: [
        "Natural Language Processing",
        "Machine Learning Solutions",
        "Predictive Analytics",
        "Process Automation",
        "Custom AI Development"
      ]
    },
    {
      title: "Mobile App Development",
      description: "Create stunning, high-performance mobile applications for iOS and Android that engage users and drive business growth.",
      iconSrc: "/skills/application.png",
      features: [
        "iOS & Android Development",
        "Cross-Platform Solutions",
        "UI/UX Design",
        "App Performance Optimization",
        "Maintenance & Support"
      ]
    },
    {
      title: "Website Design & Development",
      description: "Build responsive, modern websites with beautiful designs and seamless functionality that represent your brand perfectly.",
      iconSrc: "/skills/dns.png",
      features: [
        "Responsive Web Design",
        "E-commerce Solutions",
        "CMS Development",
        "Web Application Development",
        "Performance Optimization"
      ]
    },
    {
      title: "Social Media Development",
      description: "Enhance your social media presence with custom solutions, integrations, and strategies that connect with your audience.",
      iconSrc: "/skills/social-media.png",
      features: [
        "Social Media Strategy",
        "Content Creation",
        "Platform Integration",
        "Analytics & Reporting",
        "Campaign Management"
      ]
    },
  ];

  return (
    <section
      id="services-detail"
      ref={sectionRef}
      className="relative flex flex-col items-center justify-center py-12 sm:py-16 md:py-20 px-4 overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute top-1/4 left-0 w-48 sm:w-60 md:w-72 h-48 sm:h-60 md:h-72 bg-purple-900/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-0 w-48 sm:w-60 md:w-72 h-48 sm:h-60 md:h-72 bg-cyan-900/20 rounded-full blur-3xl"></div>

      <SectionTitle
        subtitle="What We Offer"
        title="Our Services"
        highlightWord="Services"
        description="We provide comprehensive digital solutions tailored to your business needs. Our expertise spans across multiple domains to deliver exceptional results."
        size="large"
        alignment="center"
        icon={
          <RocketLaunchIcon className="w-5 h-5" />
        }
      />

      <div className="w-full max-w-7xl mx-auto">
        <div className="flex flex-wrap justify-center -mx-2 sm:-mx-4">
          {services.map((service, index) => (
            <ServiceCard
              key={service.title}
              title={service.title}
              description={service.description}
              iconSrc={service.iconSrc}
              features={service.features}
              index={index}
            />
          ))}
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="mt-10 sm:mt-16 text-center"
      >
        <Link
          href="/projects"
          className="px-6 sm:px-8 py-2 sm:py-3 button-primary text-white rounded-lg hover:opacity-90 transition-opacity text-sm sm:text-base"
        >
          View All Projects
        </Link>
      </motion.div>
    </section>
  );
};
